{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\controllers\\membercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\controllers\\membercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efmemberworkoutprogramdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efmemberworkoutprogramdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\controllers\\memberworkoutprogramcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\controllers\\memberworkoutprogramcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\memberworkoutprogrammanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\memberworkoutprogrammanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efworkoutprogramtemplatedal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efworkoutprogramtemplatedal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\controllers\\expensescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\controllers\\expensescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\abstract\\iexpenseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\abstract\\iexpenseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 149, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "MemberController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\MemberController.cs", "RelativeDocumentMoniker": "WebAPI\\Controllers\\MemberController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\MemberController.cs", "RelativeToolTip": "WebAPI\\Controllers\\MemberController.cs", "ViewState": "AgIAAAUBAAAAAAAAAAAAACQBAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-14T05:32:16.615Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\appsettings.json", "RelativeDocumentMoniker": "WebAPI\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\appsettings.json", "RelativeToolTip": "WebAPI\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-13T20:22:12.778Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "EfMemberWorkoutProgramDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMemberWorkoutProgramDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfMemberWorkoutProgramDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMemberWorkoutProgramDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfMemberWorkoutProgramDal.cs", "ViewState": "AgIAAB8BAAAAAAAAAAAnwCgBAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T18:22:26.998Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "MemberWorkoutProgramManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MemberWorkoutProgramManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\MemberWorkoutProgramManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MemberWorkoutProgramManager.cs", "RelativeToolTip": "Business\\Concrete\\MemberWorkoutProgramManager.cs", "ViewState": "AgIAANwAAAAAAAAAAAAnwOsAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T18:22:35.845Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "MemberWorkoutProgramController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\MemberWorkoutProgramController.cs", "RelativeDocumentMoniker": "WebAPI\\Controllers\\MemberWorkoutProgramController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\MemberWorkoutProgramController.cs", "RelativeToolTip": "WebAPI\\Controllers\\MemberWorkoutProgramController.cs", "ViewState": "AgIAAIkAAAAAAAAAAAAnwJgAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T18:22:23.342Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "EfWorkoutProgramTemplateDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramTemplateDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramTemplateDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramTemplateDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramTemplateDal.cs", "ViewState": "AgIAABgAAAAAAAAAAAAnwCcAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-07T06:45:20.559Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "ExpensesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\ExpensesController.cs", "RelativeDocumentMoniker": "WebAPI\\Controllers\\ExpensesController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\ExpensesController.cs", "RelativeToolTip": "WebAPI\\Controllers\\ExpensesController.cs", "ViewState": "AgIAAGAAAAAAAAAAAAAnwHgAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-07T01:34:49.706Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "IExpenseService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Abstract\\IExpenseService.cs", "RelativeDocumentMoniker": "Business\\Abstract\\IExpenseService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Abstract\\IExpenseService.cs", "RelativeToolTip": "Business\\Abstract\\IExpenseService.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAnwBoAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-07T01:34:44.46Z"}]}]}]}