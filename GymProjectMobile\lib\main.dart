import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'core/core.dart';
import 'features/auth/presentation/providers/auth_provider.dart';

/// GymKod Pro Mobile App
///
/// Bu uygulama Angular frontend'deki tasarım sistemini takip eder.
/// Referans: GymProjectFrontend tasarım sistemi
///
/// Ana özellikler:
/// - JWT Token Authentication (Otomatik Token Refresh)
/// - QR Code sistemi
/// - Dark/Light theme support
/// - Riverpod state management
/// - Go Router navigation
/// - Production Ready

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // System UI ayarları (Angular'daki global styles'a benzer)
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    ),
  );

  // Desteklenen orientasyonlar
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Production mode'da service testleri kaldırıldı

  runApp(
    const ProviderScope(
      child: GymKodProApp(),
    ),
  );
}



class GymKodProApp extends ConsumerStatefulWidget {
  const GymKodProApp({super.key});

  @override
  ConsumerState<GymKodProApp> createState() => _GymKodProAppState();
}

class _GymKodProAppState extends ConsumerState<GymKodProApp> with WidgetsBindingObserver {
  DateTime? _lastAppResumeTime;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _lastAppResumeTime = DateTime.now();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    LoggingService.debug('App lifecycle state changed: $state', tag: 'LIFECYCLE');

    switch (state) {
      case AppLifecycleState.resumed:
        // Uygulama foreground'a döndü - kapsamlı token kontrolü
        _handleAppResumed();
        break;
      case AppLifecycleState.paused:
        // Uygulama background'a gitti - token durumunu kaydet
        _handleAppPaused();
        break;
      case AppLifecycleState.detached:
        // Uygulama kapatılıyor - cleanup
        _handleAppDetached();
        break;
      case AppLifecycleState.inactive:
        // Uygulama geçici olarak inactive (telefon çağrısı vs.)
        _handleAppInactive();
        break;
      default:
        break;
    }
  }

  /// Uygulama foreground'a döndüğünde çalışır
  Future<void> _handleAppResumed() async {
    try {
      final now = DateTime.now();
      LoggingService.authLog('App resumed', details: 'Last resume: $_lastAppResumeTime');

      final authState = ref.read(authProvider);
      if (!authState.isAuthenticated) {
        _lastAppResumeTime = now;
        return;
      }

      // Son resume'dan beri geçen süreyi kontrol et
      if (_lastAppResumeTime != null) {
        final timeSinceLastResume = now.difference(_lastAppResumeTime!);
        LoggingService.authLog('Time since last resume', details: '${timeSinceLastResume.inMinutes} minutes');

        // 2 dakikadan fazla geçtiyse token kontrolü yap (production optimized)
        if (timeSinceLastResume >= AppConstants.appResumeTokenCheckThreshold) {
          LoggingService.authLog('Performing token validation on app resume');
          await ref.read(authProvider.notifier).validateAndRefreshTokens();
        }
      } else {
        // İlk resume, token kontrolü yap
        LoggingService.authLog('First app resume, performing token validation');
        await ref.read(authProvider.notifier).validateAndRefreshTokens();
      }

      _lastAppResumeTime = now;
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'App Resume Handler');
    }
  }

  /// Uygulama background'a gittiğinde çalışır
  void _handleAppPaused() {
    try {
      LoggingService.authLog('App paused');

      final authState = ref.read(authProvider);
      if (authState.isAuthenticated) {
        // Background'da token yönetimi devam edecek
        LoggingService.authLog('App paused - token refresh service continues in background');
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'App Pause Handler');
    }
  }

  /// Uygulama kapatıldığında çalışır
  void _handleAppDetached() {
    try {
      LoggingService.authLog('App detached - performing cleanup');

      // Cleanup işlemleri burada yapılabilir
      // Token'lar secure storage'da kalacak
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'App Detach Handler');
    }
  }

  /// Uygulama geçici olarak inactive olduğunda çalışır
  void _handleAppInactive() {
    try {
      LoggingService.debug('App inactive', tag: 'LIFECYCLE');
      // Geçici inactive durumu için özel bir işlem gerekmiyor
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'App Inactive Handler');
    }
  }

  @override
  Widget build(BuildContext context) {
    final router = ref.watch(routerProvider);
    final themeMode = ref.watch(themeProvider); // Tema provider'dan tema modunu al

    // Auth state değişikliklerini dinle
    ref.listen<AuthState>(authProvider, (previous, next) {
      // Kullanıcı giriş yaptığında profil fotoğrafını refresh et
      if (previous?.isAuthenticated != next.isAuthenticated && next.isAuthenticated) {
        LoggingService.info('User logged in, refreshing profile image', tag: 'PROFILE_IMAGE');
        ref.read(profileImageProvider.notifier).forceRefreshProfileImage();
      }
    });

    return MaterialApp.router(
      // App Configuration
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,

      // Localization Configuration
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('tr', 'TR'), // Türkçe
        Locale('en', 'US'), // İngilizce
      ],
      locale: const Locale('tr', 'TR'), // Varsayılan Türkçe

      // Theme Configuration (Angular frontend'deki theme system)
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeMode, // Provider'dan gelen tema modu

      // Router Configuration (Go Router)
      routerConfig: router,
    );
  }
}