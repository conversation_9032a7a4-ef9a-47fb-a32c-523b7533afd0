/// Token Refresh Service - GymKod Pro Mobile
///
/// Bu service Angular frontend'deki token refresh timer sistemini Flutter'a uyarlar.
/// Proactive token refresh ile kullanıcı deneyimini iyileştirir.
/// Referans: GymProjectFrontend/src/app/services/auth.service.ts
library;

import 'dart:async';
import 'package:flutter/foundation.dart';
import '../constants/app_constants.dart';
import '../models/models.dart';
import 'services.dart';

/// Token Refresh Service
/// Angular frontend'deki startRefreshTokenTimer() metodunu Flutter'a uyarlar
class TokenRefreshService {
  static final TokenRefreshService _instance = TokenRefreshService._internal();
  factory TokenRefreshService() => _instance;
  TokenRefreshService._internal();

  // Services
  final StorageService _storageService = StorageService();
  final JwtService _jwtService = JwtService();
  final ApiService _apiService = ApiService();

  // Timer management
  Timer? _refreshTimer;
  bool _isRefreshing = false;
  bool _isActive = false;

  // Callbacks
  VoidCallback? _onTokenRefreshed;
  VoidCallback? _onRefreshFailed;

  /// Service'i başlat (Angular: constructor + startRefreshTokenTimer)
  Future<void> initialize({
    VoidCallback? onTokenRefreshed,
    VoidCallback? onRefreshFailed,
  }) async {
    try {
      LoggingService.authLog('TokenRefreshService initializing');

      _onTokenRefreshed = onTokenRefreshed;
      _onRefreshFailed = onRefreshFailed;
      _isActive = true;

      // Mevcut token'ı kontrol et ve timer'ı başlat
      await _checkAndStartTimer();

      LoggingService.authLog('TokenRefreshService initialized successfully');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'TokenRefreshService initialize');
    }
  }

  /// Token refresh timer'ını başlat (Angular: startRefreshTokenTimer)
  Future<void> _checkAndStartTimer() async {
    try {
      final accessToken = await _storageService.getAccessToken();
      if (accessToken == null || accessToken.isEmpty) {
        LoggingService.authLog('No access token found, skipping timer setup');
        return;
      }

      // Token'ın geçerli olup olmadığını kontrol et
      if (!_jwtService.isTokenValid(accessToken)) {
        LoggingService.authLog('Token is invalid, attempting refresh');
        await _performTokenRefresh();
        return;
      }

      // Token'ın kalan süresini hesapla
      final remainingTime = _jwtService.getTokenRemainingTime(accessToken);
      if (remainingTime == null) {
        LoggingService.authLog('Cannot determine token expiration, attempting refresh');
        await _performTokenRefresh();
        return;
      }

      // Token süresi dolmadan 1 dakika önce yenile (15 dakikalık token için)
      // Backend'de AccessTokenExpiration: 15 dakika, 1 dakika kala refresh et
      final refreshThreshold = AppConstants.tokenRefreshThreshold;
      final timeUntilRefresh = remainingTime - refreshThreshold;

      LoggingService.authLog('Token refresh timer setup',
        details: 'Remaining: ${remainingTime.inMinutes}min, Threshold: ${refreshThreshold.inMinutes}min, Until refresh: ${timeUntilRefresh.inMinutes}min');

      // Mevcut timer'ı durdur
      _stopTimer();

      // Eğer token zaten refresh edilmesi gerekiyorsa, hemen yenile
      if (timeUntilRefresh.isNegative || timeUntilRefresh.inSeconds <= 0) {
        LoggingService.authLog('Token needs immediate refresh');
        await _performTokenRefresh();
        return;
      }

      // Timer'ı başlat (Angular pattern)
      _refreshTimer = Timer(timeUntilRefresh, () async {
        LoggingService.authLog('Token refresh timer triggered');
        await _performTokenRefresh();
      });

      LoggingService.authLog('Token refresh timer started');

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'TokenRefreshService _checkAndStartTimer');
    }
  }

  /// Token refresh işlemini gerçekleştir (Angular: refreshToken)
  Future<void> _performTokenRefresh() async {
    if (_isRefreshing || !_isActive) {
      LoggingService.authLog('Token refresh skipped - already refreshing or inactive');
      return;
    }

    try {
      _isRefreshing = true;
      LoggingService.authLog('Starting token refresh');

      final refreshToken = await _storageService.getRefreshToken();
      if (refreshToken == null || refreshToken.isEmpty) {
        LoggingService.authLog('No refresh token found');
        _onRefreshFailed?.call();
        return;
      }

      // Device info al - önce storage'dan, yoksa yeni oluştur
      DeviceInfo? deviceInfo = await _storageService.getDeviceInfo();
      if (deviceInfo == null) {
        final deviceService = DeviceService();
        deviceInfo = await deviceService.getDeviceInfo();
        await _storageService.saveDeviceInfo(deviceInfo);
      }
      final deviceInfoString = deviceInfo.toDeviceInfoString();

      // Refresh token API çağrısı
      final response = await _apiService.post<Map<String, dynamic>>(
        '/auth/refresh-token',
        data: {
          'refreshToken': refreshToken,
          'deviceInfo': deviceInfoString,
        },
        isAuthFree: true, // Bu endpoint için auth header gerekmez
      );

      if (response.statusCode == 200 && response.data?['success'] == true) {
        final data = response.data!['data'];
        
        // Yeni token'ları kaydet
        await _storageService.saveAccessToken(data['token']);
        await _storageService.saveRefreshToken(data['refreshToken']);

        // User data'yı güncelle
        final userModel = _jwtService.decodeToken(data['token']);
        if (userModel != null) {
          await _storageService.saveUserData(userModel);
        }

        LoggingService.authLog('Token refreshed successfully');

        // Callback'i çağır
        _onTokenRefreshed?.call();

        // Yeni token için timer'ı yeniden başlat
        await _checkAndStartTimer();

      } else {
        LoggingService.authLog('Token refresh failed', details: response.data?['message']);
        _onRefreshFailed?.call();
      }

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'TokenRefreshService _performTokenRefresh');
      _onRefreshFailed?.call();
    } finally {
      _isRefreshing = false;
    }
  }

  /// Timer'ı durdur (Angular: stopRefreshTokenTimer)
  void _stopTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// Service'i durdur (logout sırasında)
  void stop() {
    try {
      LoggingService.authLog('TokenRefreshService stopping');
      
      _isActive = false;
      _stopTimer();
      _isRefreshing = false;
      
      LoggingService.authLog('TokenRefreshService stopped');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'TokenRefreshService stop');
    }
  }

  /// Service'i yeniden başlat (login sonrası)
  Future<void> restart() async {
    try {
      LoggingService.authLog('TokenRefreshService restarting');
      
      stop();
      await initialize(
        onTokenRefreshed: _onTokenRefreshed,
        onRefreshFailed: _onRefreshFailed,
      );
      
      LoggingService.authLog('TokenRefreshService restarted');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'TokenRefreshService restart');
    }
  }



  /// Service durumunu kontrol et
  bool get isActive => _isActive;
  bool get isRefreshing => _isRefreshing;

  /// Service'i dispose et
  void dispose() {
    LoggingService.authLog('TokenRefreshService disposing');
    stop();
  }
}
