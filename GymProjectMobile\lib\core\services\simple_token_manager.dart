import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';

import '../constants/app_constants.dart';
import '../models/user_model.dart';
import '../services/storage_service.dart';
import '../services/jwt_service.dart';
import '../services/api_service.dart';
import '../services/device_service.dart';
import '../services/logging_service.dart';

/// Simple Token Manager
/// Tek servis ile tüm token yönetimi
/// 
/// 3 Ana Fonksiyon:
/// 1. Login sonrası otomatik refresh timer kurma (14. dakikada)
/// 2. App resume'da token kontrolü ve gerekirse refresh
/// 3. Emergency refresh (API çağrısı öncesi kritik durumlarda)
class SimpleTokenManager {
  static final SimpleTokenManager _instance = SimpleTokenManager._internal();
  factory SimpleTokenManager() => _instance;
  SimpleTokenManager._internal();

  // Services
  final StorageService _storageService = StorageService();
  final JwtService _jwtService = JwtService();
  final ApiService _apiService = ApiService();

  // Timer management
  Timer? _refreshTimer;
  bool _isRefreshing = false;
  bool _isActive = false;

  // Callbacks
  VoidCallback? _onTokenRefreshed;
  VoidCallback? _onRefreshFailed;

  // Statistics
  int _totalRefreshCount = 0;
  int _successfulRefreshCount = 0;
  int _failedRefreshCount = 0;
  DateTime? _lastRefreshTime;

  /// Service'i başlat (login sonrası çağrılır)
  Future<void> initialize({
    VoidCallback? onTokenRefreshed,
    VoidCallback? onRefreshFailed,
  }) async {
    try {
      LoggingService.authLog('SimpleTokenManager initializing');

      _onTokenRefreshed = onTokenRefreshed;
      _onRefreshFailed = onRefreshFailed;
      _isActive = true;

      // Mevcut token için timer kur
      await _setupRefreshTimer();

      LoggingService.authLog('SimpleTokenManager initialized successfully');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'SimpleTokenManager initialize');
    }
  }

  /// Ana refresh timer'ını kur (15 dakikalık token için 14. dakikada refresh)
  Future<void> _setupRefreshTimer() async {
    try {
      final accessToken = await _storageService.getAccessToken();
      if (accessToken == null || accessToken.isEmpty) {
        LoggingService.authLog('No access token found, skipping timer setup');
        return;
      }

      // Token geçerli mi kontrol et
      if (!_jwtService.isTokenValid(accessToken)) {
        LoggingService.authLog('Token is invalid, attempting immediate refresh');
        await _performTokenRefresh();
        return;
      }

      // Token'ın kalan süresini hesapla
      final remainingTime = _jwtService.getTokenRemainingTime(accessToken);
      if (remainingTime == null) {
        LoggingService.authLog('Cannot determine token expiration, attempting refresh');
        await _performTokenRefresh();
        return;
      }

      // 1 dakika kala refresh et
      final timeUntilRefresh = remainingTime - AppConstants.tokenRefreshThreshold;

      LoggingService.authLog('Setting up refresh timer',
        details: 'Remaining: ${remainingTime.inMinutes}min, Until refresh: ${timeUntilRefresh.inMinutes}min');

      // Mevcut timer'ı temizle
      _clearTimer();

      // Eğer zaten refresh zamanı geldiyse hemen yap
      if (timeUntilRefresh.isNegative || timeUntilRefresh.inSeconds <= 0) {
        LoggingService.authLog('Token needs immediate refresh');
        await _performTokenRefresh();
        return;
      }

      // Timer'ı kur
      _refreshTimer = Timer(timeUntilRefresh, () async {
        LoggingService.authLog('Refresh timer triggered');
        await _performTokenRefresh();
      });

      LoggingService.authLog('Refresh timer set successfully');

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'SimpleTokenManager _setupRefreshTimer');
    }
  }

  /// Token refresh işlemini gerçekleştir
  Future<bool> _performTokenRefresh() async {
    if (_isRefreshing || !_isActive) {
      LoggingService.authLog('Token refresh skipped - already refreshing or inactive');
      return false;
    }

    try {
      _isRefreshing = true;
      _totalRefreshCount++;
      LoggingService.authLog('Starting token refresh (attempt #$_totalRefreshCount)');

      final refreshToken = await _storageService.getRefreshToken();
      if (refreshToken == null || refreshToken.isEmpty) {
        LoggingService.authLog('No refresh token found');
        _failedRefreshCount++;
        _onRefreshFailed?.call();
        return false;
      }

      // Device info al
      DeviceInfo? deviceInfo = await _storageService.getDeviceInfo();
      if (deviceInfo == null) {
        final deviceService = DeviceService();
        deviceInfo = await deviceService.getDeviceInfo();
        await _storageService.saveDeviceInfo(deviceInfo);
      }

      // Retry logic ile API çağrısı
      bool refreshSuccess = false;
      for (int attempt = 1; attempt <= AppConstants.maxTokenRefreshRetries; attempt++) {
        try {
          LoggingService.authLog('Token refresh attempt $attempt/${AppConstants.maxTokenRefreshRetries}');
          
          final response = await _apiService.post<Map<String, dynamic>>(
            '/auth/refresh-token',
            data: {
              'refreshToken': refreshToken,
              'deviceInfo': deviceInfo.toDeviceInfoString(),
            },
            isAuthFree: true,
          );

          if (response.statusCode == 200 && response.data?['success'] == true) {
            final data = response.data!['data'];
            
            // Yeni token'ları kaydet
            await _storageService.saveAccessToken(data['token']);
            await _storageService.saveRefreshToken(data['refreshToken']);

            // User data'yı güncelle
            final userModel = _jwtService.decodeToken(data['token']);
            if (userModel != null) {
              await _storageService.saveUserData(userModel);
            }

            _successfulRefreshCount++;
            _lastRefreshTime = DateTime.now();
            refreshSuccess = true;
            
            LoggingService.authLog('Token refresh successful');
            break;
          } else {
            LoggingService.authLog('Token refresh failed', details: response.data?['message']);
          }
        } catch (e) {
          LoggingService.authLog('Token refresh attempt $attempt failed', details: e.toString());
        }

        // Son deneme değilse bekle
        if (attempt < AppConstants.maxTokenRefreshRetries) {
          await Future.delayed(AppConstants.tokenRefreshRetryDelay);
        }
      }

      if (refreshSuccess) {
        _onTokenRefreshed?.call();
        // Yeni token için timer'ı yeniden kur
        await _setupRefreshTimer();
        return true;
      } else {
        _failedRefreshCount++;
        LoggingService.authLog('Token refresh failed after all attempts');
        _onRefreshFailed?.call();
        return false;
      }

    } catch (e, stackTrace) {
      _failedRefreshCount++;
      LoggingService.logException(e, stackTrace, context: 'SimpleTokenManager _performTokenRefresh');
      _onRefreshFailed?.call();
      return false;
    } finally {
      _isRefreshing = false;
    }
  }

  /// App resume'da token kontrolü yap (3 gün sonra açma senaryosu için)
  Future<bool> validateAndRefreshTokensOnAppResume() async {
    try {
      LoggingService.authLog('Validating tokens on app resume');

      final accessToken = await _storageService.getAccessToken();
      final refreshToken = await _storageService.getRefreshToken();

      if (accessToken == null || refreshToken == null) {
        LoggingService.authLog('Missing tokens during app resume validation');
        return false;
      }

      // Token geçerli mi?
      if (_jwtService.isTokenValid(accessToken)) {
        LoggingService.authLog('Access token is valid on app resume');
        
        // Yenilenmesi gerekiyor mu?
        if (_jwtService.shouldRefreshToken(accessToken)) {
          LoggingService.authLog('Token needs refresh on app resume');
          final refreshResult = await _performTokenRefresh();
          return refreshResult;
        } else {
          LoggingService.authLog('Token is valid and does not need refresh');
          // Mevcut timer'ı kontrol et ve gerekirse yeniden kur
          await _setupRefreshTimer();
          return true;
        }
      } else {
        LoggingService.authLog('Access token is invalid on app resume, attempting refresh');
        // Token geçersiz, refresh dene
        final refreshResult = await _performTokenRefresh();
        return refreshResult;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'SimpleTokenManager validateAndRefreshTokensOnAppResume');
      return false;
    }
  }

  /// Emergency refresh (API çağrısı öncesi kritik durumlarda)
  Future<bool> performEmergencyRefreshIfNeeded() async {
    try {
      final accessToken = await _storageService.getAccessToken();
      if (accessToken == null) return false;

      // Sadece 30 saniye kala emergency refresh
      if (_jwtService.isTokenInCriticalState(accessToken)) {
        LoggingService.authLog('Performing emergency token refresh');
        return await _performTokenRefresh();
      }

      return true; // Token henüz kritik durumda değil
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'SimpleTokenManager performEmergencyRefreshIfNeeded');
      return false;
    }
  }

  /// Timer'ı temizle
  void _clearTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// Service'i durdur (logout sırasında)
  void stop() {
    try {
      LoggingService.authLog('SimpleTokenManager stopping');
      
      _isActive = false;
      _clearTimer();
      _isRefreshing = false;
      
      LoggingService.authLog('SimpleTokenManager stopped');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'SimpleTokenManager stop');
    }
  }

  /// Service'i yeniden başlat (login sonrası)
  Future<void> restart({
    VoidCallback? onTokenRefreshed,
    VoidCallback? onRefreshFailed,
  }) async {
    try {
      LoggingService.authLog('SimpleTokenManager restarting');
      
      stop();
      await initialize(
        onTokenRefreshed: onTokenRefreshed ?? _onTokenRefreshed,
        onRefreshFailed: onRefreshFailed ?? _onRefreshFailed,
      );
      
      LoggingService.authLog('SimpleTokenManager restarted');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'SimpleTokenManager restart');
    }
  }

  /// Debug bilgileri al
  Map<String, dynamic> getDebugInfo() {
    return {
      'isActive': _isActive,
      'isRefreshing': _isRefreshing,
      'hasActiveTimer': _refreshTimer != null,
      'totalRefreshCount': _totalRefreshCount,
      'successfulRefreshCount': _successfulRefreshCount,
      'failedRefreshCount': _failedRefreshCount,
      'lastRefreshTime': _lastRefreshTime?.toIso8601String(),
      'successRate': _totalRefreshCount > 0 ? (_successfulRefreshCount / _totalRefreshCount * 100).toStringAsFixed(1) + '%' : 'N/A',
    };
  }

  /// Service durumunu kontrol et
  bool get isActive => _isActive;
  bool get isRefreshing => _isRefreshing;
  bool get hasActiveTimer => _refreshTimer != null;

  /// Service'i dispose et
  void dispose() {
    LoggingService.authLog('SimpleTokenManager disposing');
    stop();
  }
}
